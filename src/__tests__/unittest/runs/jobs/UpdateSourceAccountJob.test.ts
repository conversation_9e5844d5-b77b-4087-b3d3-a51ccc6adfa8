import {ScraperConfigurationManager} from '../../../../configurations/ScraperConfigurationManager';
import {LoginParams} from '../../../../dependencies/BinaryProxy';
import {ScrapeResult} from '../../../../processes/types';
import {ScrapeRunContext} from '../../../../runs/jobs/context';
import {UpdateSourceAccountJob} from '../../../../runs/jobs/UpdateSourceAccountJob';
import {UploadReportJob} from '../../../../runs/jobs/UploadReportJob';
import {Source} from '../../../../types';
import {mockScraperServiceClient} from '../../../utils/helpers';
import {mockReportInfo, mockReportUpload} from '../../../utils/scraperRunMocks';

describe('UpdateSourceAccountJob', () => {
    let uploadReportSpy: jest.SpyInstance;
    let scraperConfigurationManagerSpy: jest.SpyInstance<ScraperConfigurationManager>;
    let updateSourceAccountJob: UpdateSourceAccountJob;
    const mockLoginParams: LoginParams = {
        credentials: {
            username: 'test-username',
            password: 'test-password'
        },
        sessionPath: 'test-session-path'
    };

    beforeEach(() => {
        updateSourceAccountJob = new UpdateSourceAccountJob(scraperConfigurationManagerSpy, {} as LoginParams, 'test-account-id');
    });

    afterEach(() => {
        jest.restoreAllMocks();
    });

    test('UploadReportJob calls uploadReport for each scrape result and sendReportUploadInfo for non-shadow runs', async () => {
        const context = {latestScrapeResults} as unknown as ScrapeRunContext;
        await uploadReportJob.execute(context);

        expect(uploadReportSpy).toHaveBeenCalledTimes(2);
        expect(uploadReportSpy).toHaveBeenCalledWith('mainDir', latestScrapeResults[0]);
        expect(uploadReportSpy).toHaveBeenCalledWith('mainDir', latestScrapeResults[1]);

        expect(sendReportUploadInfoSpy).toHaveBeenCalledTimes(2);
    });

    test('UploadReportJob skips sendReportUploadInfo in shadow run mode', async () => {
        const context = {latestScrapeResults, isShadowRun: true} as unknown as ScrapeRunContext;
        await uploadReportJob.execute(context);

        expect(uploadReportSpy).toHaveBeenCalledTimes(2);
        expect(uploadReportSpy).toHaveBeenCalledWith('mainDir', latestScrapeResults[0]);
        expect(uploadReportSpy).toHaveBeenCalledWith('mainDir', latestScrapeResults[1]);

        expect(sendReportUploadInfoSpy).not.toHaveBeenCalled();
    });

    test('UploadReportJob works with single scrape result', async () => {
        const singleResult = [{report: 'dummy'} as any as ScrapeResult];
        const context = {latestScrapeResults: singleResult} as unknown as ScrapeRunContext;

        await uploadReportJob.execute(context);

        expect(uploadReportSpy).toHaveBeenCalledTimes(1);
        expect(uploadReportSpy).toHaveBeenCalledWith('mainDir', singleResult[0]);
        expect(sendReportUploadInfoSpy).toHaveBeenCalledTimes(1);
    });

    test('UploadReportJob sends ReportUploadEvent on successful upload', async () => {
        const context = {
            latestScrapeResults,
            isShadowRun: false,
            operationId: 'test-operation'
        } as unknown as ScrapeRunContext;

        await uploadReportJob.execute(context);
        await uploadReportJob.onSuccess(context);

        expect(sendReportUploadInfoSpy).toHaveBeenCalledTimes(2);
        expect(mockScraperServiceClient.scheduleReportUploadEvent).toHaveBeenCalledTimes(2);

        const calls = (mockScraperServiceClient.scheduleReportUploadEvent as jest.Mock).mock.calls;

        expect(calls[0][0]).toEqual({
            scrapeResult: latestScrapeResults[0],
            reportInfo: mockReportInfo,
            operationId: 'test-operation'
        });
        expect(calls[1][0]).toEqual({
            scrapeResult: latestScrapeResults[1],
            reportInfo: mockReportInfo,
            operationId: 'test-operation'
        });
    });
});
