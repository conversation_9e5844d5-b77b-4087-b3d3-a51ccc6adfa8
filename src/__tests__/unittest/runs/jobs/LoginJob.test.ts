import {BinaryProxy, LoginParams} from '../../../../dependencies/BinaryProxy';
import {BinaryLoginResult} from '../../../../processes/types';
import {LoginJob, LoginJobContext} from '../../../../runs/jobs/LoginJob';
import {Command} from '../../../../types';
import {nextEventLoopTick} from '../../../../utils/asyncUtils';
import {Deferred} from '../../../../utils/Deferred';

describe('LoginJob', () => {
    afterEach(jest.restoreAllMocks);

    const loginParams = {credentials: {username: 'test', password: 'test'}} as any as LoginParams;

    const createMockBinaryProxy = (result: Promise<any>): jest.Mocked<BinaryProxy> => {
        return {run: jest.fn().mockResolvedValue({result})} as any;
    };

    test('should login with credendials successfully and set sessionValid to true', async () => {
        const context = {operationId: 'fake'} as LoginJobContext;
        const loginResult: BinaryLoginResult = {id: 'user123'};
        const binaryProxyMock = createMockBinaryProxy(Promise.resolve(loginResult));
        const job = new LoginJob(binaryProxyMock, loginParams);

        const result = await job.execute(context);

        expect(binaryProxyMock.run).toHaveBeenCalledWith(Command.LOGIN, loginParams, context.operationId);
        expect(result).toBe(loginResult);
        expect(context).toStrictEqual({operationId: 'fake', sessionValid: true, checkSessionResult: loginResult});
    });

    test('should throw "Session expired" error when credentials are missing', async () => {
        const context = {operationId: 'fake'} as LoginJobContext;
        const binaryProxyMock = createMockBinaryProxy(Promise.resolve({}));
        const job = new LoginJob(binaryProxyMock, {} as LoginParams);

        await expect(job.execute(context)).rejects.toThrow('Session expired');
        expect(binaryProxyMock.run).not.toHaveBeenCalled();
    });

    test('should return correct value from shouldRun based on sessionValid', () => {
        const job = new LoginJob({} as BinaryProxy, {} as LoginParams);

        expect(job.shouldRun({sessionValid: undefined} as LoginJobContext, [])).toBe(true);
        expect(job.shouldRun({sessionValid: false} as LoginJobContext, [])).toBe(true);
        expect(job.shouldRun({sessionValid: true} as LoginJobContext, [])).toBe(false);
    });

    test('should call execution.kill when killed during execution', async () => {
        const context = {operationId: 'fake'} as LoginJobContext;
        const executionResultDeferred = new Deferred<BinaryLoginResult>();
        const mockExecution = {result: executionResultDeferred.promise, kill: jest.fn()};
        const binaryProxyRunDeferred = new Deferred<typeof mockExecution>();
        const binaryProxyMock = {run: jest.fn().mockReturnValue(binaryProxyRunDeferred.promise)} as any;
        const job = new LoginJob(binaryProxyMock, loginParams);

        const executePromise = job.execute(context);
        binaryProxyRunDeferred.resolve(mockExecution);
        await nextEventLoopTick();
        await job.kill(context);
        executionResultDeferred.resolve({} as BinaryLoginResult);

        expect(mockExecution.kill).toHaveBeenCalledWith(context.operationId);
        await executePromise;
    });
});
